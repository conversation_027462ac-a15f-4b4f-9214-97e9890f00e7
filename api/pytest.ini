[pytest]
addopts = --cov=./api --cov-report=json --cov-report=xml
env =
    ANTHROPIC_API_KEY = sk-ant-api11-IamNotARealKeyJustForMockTestKawaiiiiiiiiii-NotBaka-ASkksz
    AZURE_OPENAI_API_BASE = https://difyai-openai.openai.azure.com
    AZURE_OPENAI_API_KEY = xxxxb1707exxxxxxxxxxaaxxxxxf94
    CHATGLM_API_BASE = http://a.abc.com:11451
    CODE_EXECUTION_API_KEY = dify-sandbox
    CODE_EXECUTION_ENDPOINT = http://127.0.0.1:8194
    CODE_MAX_STRING_LENGTH = 80000
    PLUGIN_DAEMON_KEY=lYkiYYT6owG+71oLerGzA7GXCgOT++6ovaezWAjpCjf+Sjc3ZtU+qUEi
    PLUGIN_DAEMON_URL=http://127.0.0.1:5002
    PLUGIN_MAX_PACKAGE_SIZE=15728640
    INNER_API_KEY_FOR_PLUGIN=QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1
    MARKETPLACE_ENABLED=true
    MARKETPLACE_API_URL=https://marketplace.dify.ai
    FIRECRAWL_API_KEY = fc-
    FIREWORKS_API_KEY = fw_aaaaaaaaaaaaaaaaaaaa
    GOOGLE_API_KEY = abcdefghijklmnopqrstuvwxyz
    HUGGINGFACE_API_KEY = hf-awuwuwuwuwuwuwuwuwuwuwuwuwuwuwuwuwu
    HUGGINGFACE_EMBEDDINGS_ENDPOINT_URL = c
    HUGGINGFACE_TEXT2TEXT_GEN_ENDPOINT_URL = b
    HUGGINGFACE_TEXT_GEN_ENDPOINT_URL = a
    MIXEDBREAD_API_KEY = mk-aaaaaaaaaaaaaaaaaaaa
    MOCK_SWITCH = true
    NOMIC_API_KEY = nk-aaaaaaaaaaaaaaaaaaaa
    OPENAI_API_KEY = sk-IamNotARealKeyJustForMockTestKawaiiiiiiiiii
    TEI_EMBEDDING_SERVER_URL = http://a.abc.com:11451
    TEI_RERANK_SERVER_URL = http://a.abc.com:11451
    TEI_API_KEY = ttttttttttttttt
    UPSTAGE_API_KEY = up-aaaaaaaaaaaaaaaaaaaa
    VOYAGE_API_KEY = va-aaaaaaaaaaaaaaaaaaaa
    XINFERENCE_CHAT_MODEL_UID = chat
    XINFERENCE_EMBEDDINGS_MODEL_UID = embedding
    XINFERENCE_GENERATION_MODEL_UID = generate
    XINFERENCE_RERANK_MODEL_UID = rerank
    XINFERENCE_SERVER_URL = http://a.abc.com:11451
    GITEE_AI_API_KEY = aaaaaaaaaaaaaaaaaaaa
