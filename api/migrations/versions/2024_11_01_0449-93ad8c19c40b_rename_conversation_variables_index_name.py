"""rename conversation variables index name

Revision ID: 93ad8c19c40b
Revises: d3f6769a94a3
Create Date: 2024-11-01 04:49:53.100250

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '93ad8c19c40b'
down_revision = 'd3f6769a94a3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    if conn.dialect.name == 'postgresql':
        # Rename indexes for PostgreSQL
        op.execute('ALTER INDEX workflow__conversation_variables_app_id_idx RENAME TO workflow_conversation_variables_app_id_idx')
        op.execute('ALTER INDEX workflow__conversation_variables_created_at_idx RENAME TO workflow_conversation_variables_created_at_idx')
    else:
        # For other databases, use the original drop and create method
        with op.batch_alter_table('workflow_conversation_variables', schema=None) as batch_op:
            batch_op.drop_index('workflow__conversation_variables_app_id_idx')
            batch_op.drop_index('workflow__conversation_variables_created_at_idx')
            batch_op.create_index(batch_op.f('workflow_conversation_variables_app_id_idx'), ['app_id'], unique=False)
            batch_op.create_index(batch_op.f('workflow_conversation_variables_created_at_idx'), ['created_at'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    if conn.dialect.name == 'postgresql':
        # Rename indexes back for PostgreSQL
        op.execute('ALTER INDEX workflow_conversation_variables_app_id_idx RENAME TO workflow__conversation_variables_app_id_idx')
        op.execute('ALTER INDEX workflow_conversation_variables_created_at_idx RENAME TO workflow__conversation_variables_created_at_idx')
    else:
        # For other databases, use the original drop and create method
        with op.batch_alter_table('workflow_conversation_variables', schema=None) as batch_op:
            batch_op.drop_index(batch_op.f('workflow_conversation_variables_created_at_idx'))
            batch_op.drop_index(batch_op.f('workflow_conversation_variables_app_id_idx'))
            batch_op.create_index('workflow__conversation_variables_created_at_idx', ['created_at'], unique=False)
            batch_op.create_index('workflow__conversation_variables_app_id_idx', ['app_id'], unique=False)

    # ### end Alembic commands ###
